<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  >
    <template #toolbar>
      <slot name="toolbar"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    configName: '',
    configKey: ''
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '参数名称',
      prop: 'configName',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '参数键名',
      prop: 'configKey',
      placeholder: '请输入'
    },
    {
      type: 'daterange',
      label: '创建时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', { ...form, params: { beginTime: d1, endTime: d2 } });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
