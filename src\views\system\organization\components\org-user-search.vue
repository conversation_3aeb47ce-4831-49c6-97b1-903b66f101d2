<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const { form, resetFields } = useFormData({
    username: '',
    nickname: '',
    sex: void 0
  });

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '用户账号',
      prop: 'username',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '用户名',
      prop: 'nickname',
      placeholder: '请输入'
    },
    {
      type: 'dictSelect',
      label: '性别',
      prop: 'sex',
      placeholder: '请选择',
      props: { code: 'sex' }
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索 */
  const search = () => {
    emit('search', form);
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
