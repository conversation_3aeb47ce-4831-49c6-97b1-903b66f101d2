<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  >
    <template #toolbar>
      <slot name="exportData"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useDictData } from '@/utils/use-dict-data';
  //获取父组件参数
  const props = defineProps({
    /** 默认查询参数 */
    currentStatus: String
  });
  //监听currentStatus
  watch(
    () => props.currentStatus,
    (val) => {
      form.objectName = null;
    }
  );
  const emit = defineEmits(['search']);
  /** 字典数据 */
  const [objectName] = useDictData(['object_name']);
  /** 表单数据 */
  const [form, resetFields] = useFormData({
    title: '',
    operName: '',
    operationName: '',
    objectName: ''
  });

  const getDicts = () => {
    try {
      if (props.currentStatus == 'project') {
        return objectName.value
          .filter((val) => val.remark != '非项目')
          .map((val) => {
            return {
              label: val.dictLabel,
              value: val.dictValue
            };
          });
      }
      return objectName.value
        .filter((val) => val.remark == '非项目')
        .map((val) => {
          return {
            label: val.dictLabel,
            value: val.dictValue
          };
        });
    } catch (error) {
      return [];
    }
  };

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'select',
      label: '操作类型',
      prop: 'operationName',
      placeholder: '请选择操作类型',
      options: [
        { label: '新增', value: 'add' },
        { label: '修改', value: 'edit' },
        { label: '删除', value: 'delete' }
      ]
    },
    {
      type: 'select',
      label: '系统模块',
      prop: 'objectName',
      placeholder: '请选择',
      filterable: true,
      options: getDicts()
    },
    {
      type: 'date',
      label: '操作时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };
  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      params: {
        beginTime: d1 ? `${d1} 00:00:00` : '',
        endTime: d2 ? `${d2} 23:59:59` : ''
      }
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
