<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :items="searchItems"
    :show-label="false"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  >
    <template #toolbar>
      <slot name="toolbar"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    title: '',
    operName: ''
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '系统模块',
      prop: 'title',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '操作人员',
      prop: 'operName',
      placeholder: '请输入'
    },
    {
      type: 'daterange',
      label: '操作时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      params: {
        beginTime: d1 ? `${d1} 00:00:00` : '',
        endTime: d2 ? `${d2} 23:59:59` : ''
      }
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
