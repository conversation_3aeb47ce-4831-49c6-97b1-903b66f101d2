<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <EleTableSearch
      :model="form"
      :show-label="false"
      :items="searchItems"
      @updateValue="updateFormValue"
      @submit="search"
      @reset="reset"
    />
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const props = defineProps({
    /** 默认搜索条件 */
    where: Object
  });

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    username: '',
    nickname: '',
    ...(props.where || {})
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '用户账号',
      prop: 'username',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '用户名',
      prop: 'nickname',
      placeholder: '请输入'
    },
    {
      type: 'daterange',
      label: '登录时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
