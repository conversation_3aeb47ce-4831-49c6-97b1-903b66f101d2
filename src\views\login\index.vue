<template>
  <div class="login-wrapper">
    <div class="model" v-if="showQuickLogin">
      <div class="ewm">
        <h3> 快捷登录 </h3>
        <dl>
          <span v-for="item in quickLoginList" :key="item">
            <dt>&gt;&nbsp;&nbsp;{{ item.role }}</dt>
            <dd @click="doQuickLogin(item.phone)">账号：{{ item.name }}</dd>
          </span>
        </dl>
      </div>
    </div>
    <ele-card shadow="always" class="login-card">
      <div class="login-cover"></div>
      <div class="login-body">
        <div style="text-align: center">
          <h2>阳光德美项目物资管理系统</h2>
        </div>
        <div style="margin: 30px 0">
          <el-divider>
            <span style="font-size: 18px; font-weight: bold"> 用户登录 </span>
          </el-divider>
        </div>
        <div style="margin-top: 50px">
          <el-form
            ref="formRef"
            size="large"
            :model="form"
            :rules="rules"
            @keyup.enter="submit"
          >
            <el-form-item prop="username">
              <el-input
                clearable
                v-model="form.username"
                placeholder="请输入登录账号"
                :prefix-icon="User"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                show-password
                v-model="form.password"
                placeholder="请输入登录密码"
                :prefix-icon="Lock"
              />
            </el-form-item>
            <el-form-item prop="code">
              <div class="login-captcha-group">
                <el-input
                  clearable
                  v-model="form.code"
                  placeholder="请输入验证码"
                  :prefix-icon="ProtectOutlined"
                />
                <div class="login-captcha" @click="changeCaptcha">
                  <img v-if="captcha" :src="captcha" />
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <div
                style="
                  width: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <el-checkbox v-model="form.rememberMe">记住密码</el-checkbox>
                <span class="forgetPwd" @click="forgetPwdChange">忘记密码</span>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                size="large"
                type="primary"
                :loading="loading"
                style="width: 100%; margin-top: 10px"
                @click="submit"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- <div v-else class="login-qrcode-group">
          <ele-qr-code-svg
            :size="180"
            :margin="2"
            :value="qrcode"
            class="login-qrcode"
          />
          <div style="margin-top: 16px; cursor: pointer" @click="refreshQrCode">
            <el-icon :size="17" style="vertical-align: -3px; margin-right: 6px">
              <refresh-right />
            </el-icon>
            <span>刷新二维码</span>
          </div>
        </div> -->
      </div>
    </ele-card>
    <div
      style="
        color: #fff;
        width: 100%;
        height: 46px;
        line-height: 46px;
        text-align: center;
      "
      >Copyright ©
      北京阳光德美医药科技有限公司版权所有，技术支持：北京中兴正远科技有限公司</div
    >
  </div>
  <!-- <ele-modal
    :width="450"
    :model-value="modelValue"
    title="重置密码"
    @closed="modelValue = false"
    :draggable="false"
  >
    <div>
      <el-form ref="pwdFormRef" :model="pwdForm" :rules="forgetRules">
        <el-form-item prop="phone">
          <el-input
            v-model="pwdForm.phone"
            placeholder="请输入手机号码"
            :prefix-icon="Iphone"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="smsCode">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <el-input
              v-model="pwdForm.smsCode"
              placeholder="请输入验证码"
              size="large"
              style="width: 255px"
            />
            <div class="forget-code">
              <el-button
                v-if="countdown == 60"
                size="large"
                type="primary"
                style="
                  width: 100%;
                  height: 40px;
                  font-size: 14px;
                  margin-left: 20px !important;
                "
                @click="sendSmsChange"
                >获取验证码</el-button
              >
              <el-button
                v-else
                size="large"
                type="primary"
                style="
                  width: 80%;
                  height: 40px;
                  font-size: 14px;
                  margin-left: 20px !important;
                "
                >{{ countdown }}秒后重新获取</el-button
              >
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="newPassword">
          <el-input
            type="password"
            v-model="pwdForm.newPassword"
            placeholder="请输入新密码"
            :prefix-icon="Lock"
            autocomplete="new-password"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="newPassword2">
          <el-input
            type="password"
            v-model="pwdForm.newPassword2"
            placeholder="请输入确认新密码"
            :prefix-icon="Lock"
            autocomplete="new-password"
            size="large"
          />
        </el-form-item>
        <div>新密码必须同时包含字母、数字和特殊字符其中三项且至少8位！ </div>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="modelValue = false">取 消</el-button>
      <el-button type="primary" @click="togoConfirm()">确 定</el-button>
    </template>
  </ele-modal> -->
</template>

<script setup>
  import {
    appExupdatePwd,
    getCodeImg,
    login,
    sendSms,
    quickLogin
  } from '@/api/login';
  import { ProtectOutlined } from '@hnjing/zxzy-admin-plus/es/icons';
  import { decrypt, encrypt } from '@/utils/jsencrypt';
  import { getToken } from '@/utils/token-util';
  import { CLIENT_ID } from '@/config/setting';
  import { usePageTab } from '@/utils/use-page-tab';
  import { Iphone, Lock, User } from '@element-plus/icons-vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import Cookies from 'js-cookie';
  import { onMounted, reactive, ref, unref, getCurrentInstance } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { useRouter } from 'vue-router';

  const { currentRoute, push } = useRouter();
  const { goHomeRoute, cleanPageTabs } = usePageTab();
  const userStore = useUserStore();
  // const instance = getCurrentInstance();

  /** 表单 */
  const formRef = ref();
  const pwdFormRef = ref();
  /** 加载状态 */
  const loading = ref(false);
  // const modelValue = ref(false);
  // const countdown = ref(60);
  // const timer = ref(null);
  /** 快捷登录列表:
   *  张三 CRC 13112345678 a12345678
      李四 研究者 14012345678
      王五 物资管理员15012345678
      赵六 样品管理员16012345678
      孙七 财务经理 17012345678
      王婷 项目管理员18000000001 
      张三丰 公司管理层 18000000002
      陈梦 PM 18000000003
      王维 客户项目经理 18000000004
      李子柒 商务经理 18000000005
      周天 QA 18000000006
      刘艺 QC 18000000007
      陈生 系统管理员 18000000008
   */

  const quickLoginList = ref([
    {
      name: '管理员',
      role: '系统管理员',
      phone: 'admin'
    },
    {
      name: '张三丰',
      role: '公司管理层',
      phone: '18000000002'
    },
    {
      name: '孙七',
      role: '财务经理',
      phone: '17012345678'
    },
    {
      name: '王婷',
      role: '项目管理员',
      phone: '18000000001'
    },
    {
      name: '陈梦',
      role: '项目负责人',
      phone: '18000000003'
    },
    {
      name: '李子柒',
      role: '商务经理',
      phone: '18000000005'
    },
    {
      name: '王五',
      role: '物资管理员',
      phone: '15012345678'
    },
    {
      name: '赵六',
      role: '样品管理员',
      phone: '16012345678'
    },
    {
      name: '周天',
      role: 'QA',
      phone: '18000000006'
    },
    {
      name: '刘艺',
      role: 'QC',
      phone: '18000000007'
    },
    {
      name: '王维',
      role: '客户项目经理',
      phone: '18000000004'
    },
    {
      name: '李四',
      role: '研究者',
      phone: '14012345678'
    },
    {
      name: '张三',
      role: 'CRC',
      phone: '13112345678'
    },
    {
      name: '陈生',
      role: 'CRA',
      phone: '18000000008'
    }
  ]);
  /** 表单数据 */
  const form = reactive({
    // tenantId: 4, // 租户id, 不需要可去掉
    clientId: CLIENT_ID,
    grantType: 'password',
    username: '',
    password: '',
    code: '',
    uuid: '',
    rememberMe: true
  });
  // const pwdForm = reactive({
  //   phone: '',
  //   newPassword: '',
  //   newPassword2: '',
  //   smsCode: '',
  //   tempCheckCode: ''
  // });
  /** 表单验证规则 */
  const rules = reactive({
    username: [
      {
        required: true,
        message: '请输入登录账号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入登录密码',
        type: 'string',
        trigger: 'blur'
      }
    ]
    // code: [
    //   {
    //     required: true,
    //     message: '请输入验证码',
    //     type: 'string',
    //     trigger: 'blur'
    //   }
    // ]
  });
  // const forgetRules = reactive({
  //   phone: [
  //     {
  //       required: true,
  //       message: '请输入手机号码',
  //       type: 'string',
  //       trigger: 'blur'
  //     }
  //   ],
  //   newPassword: [
  //     {
  //       required: true,
  //       message: '请输入新密码',
  //       type: 'string',
  //       trigger: 'blur'
  //     }
  //   ],
  //   newPassword2: [
  //     {
  //       required: true,
  //       message: '请输入确认密码',
  //       type: 'string',
  //       trigger: 'blur'
  //     }
  //   ],
  //   smsCode: [
  //     {
  //       required: true,
  //       message: '请输入验证码',
  //       type: 'string',
  //       trigger: 'blur'
  //     }
  //   ]
  // });
  /** 图形验证码 */
  const captcha = ref('');

  /** 验证码内容, 实际项目去掉 */
  // const text = ref('');

  /** 二维码 */
  // const qrcode = ref('');

  /** 提交 */
  const submit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return false;
      }
      // if (form.code.toLowerCase() !== text.value) {
      //   EleMessage.error('验证码错误');
      //   return;
      // }
      if (form.rememberMe) {
        Cookies.set('username', form.username, { expires: 30 });
        Cookies.set('password', form.password, {
          expires: 30
        });
        Cookies.set('rememberMe', form.rememberMe, { expires: 30 });
      } else {
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }
      loading.value = true;
      const params = {
        ...form,
        password: form.password
      };
      login(params)
        .then(() => {
          EleMessage.success('登录成功');
          cleanPageTabs();
          goHome();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e);
          changeCaptcha();
        });
    });
  };
  //sendSms获取手机验证码
  // const sendSmsChange = () => {
  //   console.log('diamle');
  //   if (!pwdForm.phone) return EleMessage.error('手机号码不能为空');
  //   if (pwdForm.phone) {
  //     const reg2 = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/;
  //     if (!reg2.test(pwdForm.phone))
  //       return EleMessage.error('手机号码输入不正确');
  //   }
  //   sendSms(pwdForm.phone).then((res) => {
  //     console.log('获取验证码---》', res);
  //     if (res.code == 200) {
  //       pwdForm.smsCode = res.data.smsCode;
  //       pwdForm.tempCheckCode = res.data.tempCheckCode;
  //     }
  //   });
  //   timer.value = setInterval(() => {
  //     if (countdown.value > 1) {
  //       countdown.value--;
  //     } else {
  //       clearInterval(timer.value);
  //       timer.value = null;
  //       countdown.value = 60;
  //     }
  //   }, 1000);
  // };
  // const togoConfirm = () => {
  //   pwdFormRef.value?.validate?.((valid) => {
  //     if (!valid) {
  //       return false;
  //     }
  //     console.log('提交参数---》', pwdForm);
  //     if (pwdForm.newPassword) {
  //       const reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,16}$/;
  //       if (!reg.test(pwdForm.newPassword))
  //         return EleMessage.error('密码输入格式错误');
  //     }
  //     if (pwdForm.newPassword2) {
  //       const reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,16}$/;
  //       if (!reg.test(pwdForm.newPassword2))
  //         return EleMessage.error('确认密码输入格式错误');
  //     }
  //     if (pwdForm.newPassword != pwdForm.newPassword2) {
  //       return EleMessage.error('两次密码输入不一致');
  //     }
  //     let params = {
  //       phone: pwdForm.phone,
  //       newPassword: pwdForm.newPassword,
  //       smsCode: pwdForm.smsCode,
  //       tempCheckCode: pwdForm.tempCheckCode,
  //       flag: true
  //     };
  //     appExupdatePwd(params).then((res) => {
  //       console.log('dddddd', res);
  //       if (res.code == 200) {
  //         EleMessage.success('密码修改成功');
  //         modelValue.value = false;
  //       } else {
  //         EleMessage.error(res.msg);
  //       }
  //     });
  //   });
  // };
  /** 获取图形验证码 */
  const changeCaptcha = () => {
    getCodeImg()
      .then((res) => {
        captcha.value = 'data:image/gif;base64,' + res.data.img;
        form.uuid = res.data.uuid;
      })
      .catch((e) => {
        EleMessage.error(e);
      });
  };
  const forgetPwdChange = () => {
    pwdFormRef.value?.clearValidate();
    // modelValue.value = true;
    EleMessage.warning(
      '忘记密码，请备注（姓名/账号）发送邮件（<EMAIL>，<EMAIL>）进行重置！'
    );
  };

  /** 刷新二维码 */
  // const refreshQrCode = () => {
  //   qrcode.value = `https://api.eleadmin.com/v2/auth/login?code=${uuid()}`;
  // };

  /** 选项卡切换事件 */
  // const onTabChange = (active) => {
  //   if (active === 2) {
  //     refreshQrCode();
  //   }
  // };
  const getCookie = () => {
    const username = Cookies.get('username');
    const password = Cookies.get('password');
    const rememberMe = Cookies.get('rememberMe');
    (form.username = username === undefined ? form.username : username),
      (form.password =
        password === undefined ? form.password : decrypt(password)),
      (form.rememberMe =
        rememberMe === undefined ? false : Boolean(rememberMe));
  };

  /** 跳转到首页 */
  const goHome = () => {
    const { query } = unref(currentRoute);
    // if (!Cookies.get('index')) {
    //   push('/home');
    //   return;
    // }
    // if (Cookies.get('index')) {
    //   Cookies.set('index', Cookies.get('index'), { expires: 7 });
    // }

    goHomeRoute(query.from);
    // if (
    //   decodeURIComponent(query.from)?.startsWith('/project/') &&
    //   userStore.projectId
    // ) {
    //   userStore.setUserInfoByProject(userStore.projectId, userStore.backPath);
    // }
  };
  const doQuickLogin = (uname) => {
    quickLogin(uname)
      .then(() => {
        EleMessage.success('快捷登录成功');
        setTimeout(() => {
          location.reload();
        }, 500);
      })
      .catch((e) => {
        EleMessage.error(e);
      });
  };
  //
  if (getToken()) {
    goHome();
  } else {
    changeCaptcha();
  }
  const showQuickLogin = ref(false);
  onMounted(() => {
    if (import.meta.env.VITE_USER_NODE_ENV !== 'production') {
      showQuickLogin.value = true;
    }
    getCookie();
  });
</script>

<style lang="scss" scoped>
  .login-wrapper {
    min-height: 100vh;
    box-sizing: border-box;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-image: url('/src/assets/login/bg-b.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .login-card {
      width: 920px;
      max-width: 100%;
      overflow: hidden;

      :deep(.ele-card-body) {
        display: flex;
        padding: 0;
        height: 512px;
      }
    }
  }

  .login-cover {
    flex: 1;
    box-sizing: border-box;
    background-image: url('/src/assets/login/bg-zxzy2.png');
    background-repeat: no-repeat;
    // padding: 36px 8px;
    // background-color: #1681fd;
    // background-position: bottom;
    background-size: 100% 100%;
    // text-align: center;
  }

  .login-body {
    width: 538px;
    flex-shrink: 0;
    padding: 32px 88px 0 88px;
    box-sizing: border-box;

    :deep(.el-checkbox) {
      height: auto;

      .el-checkbox__label {
        color: inherit;
      }
    }

    :deep(.el-input__prefix-inner > .el-icon) {
      margin-right: 12px;
      transform: scale(1.26) translateY(-1px);
    }
  }

  /* 验证码 */
  .login-captcha-group {
    width: 100%;
    display: flex;
    align-items: center;

    :deep(.el-input) {
      flex: 1;
    }

    .login-captcha {
      width: 108px;
      height: 40px;
      margin-left: 8px;
      border-radius: 4px;
      //border: 1px solid hsla(0, 0%, 60%, 0.46);
      transition: border 0.2s;
      box-sizing: border-box;
      background: #fff;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block;
      }

      &:hover {
        border-color: hsla(0, 0%, 60%, 0.8);
      }
    }

    :deep(.el-input__prefix-inner > .el-icon) {
      transform: scale(1.16);
    }
  }

  /* 标题 */
  .login-title {
    color: rgba(255, 255, 255, 0.98);
    font-size: 26px;
    margin: 0 0 6px 0;
    font-weight: normal;
    font-family: 'AliPuHui';
    letter-spacing: 1.2px;
  }

  .login-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
    font-weight: normal;
    font-family: 'AliPuHui';
    letter-spacing: 4px;
  }

  /* 阿里巴巴普惠体 */
  @font-face {
    font-family: 'AliPuHui';
    font-weight: 300;
    src: (
      url('//at.alicdn.com/wf/webfont/jWZHcEP2lzge/5AfKUTWZEo8W.woff2')
        format('woff2'),
      url('//at.alicdn.com/wf/webfont/jWZHcEP2lzge/Dvhs41TtRdYF.woff')
        format('woff')
    );
    font-display: swap;
  }

  /* 二维码 */
  .login-qrcode-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 0;
  }

  .login-qrcode {
    font-size: 0;
    display: inline-block;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
  }
  .forgetPwd {
    color: #6eacfb;
    cursor: pointer;
  }
  .model {
    position: fixed;
    //left: calc(50% + 486px);
    top: 20px;
    right: 20px;
    z-index: 110000;
  }

  .ewm {
    width: 120px;
    background: rgba(0, 0, 0, 0.5);
    border-color: #513a29;
    border-radius: 10px;
    margin: -9px auto 0;
    padding: 10px;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
  }

  .ewm h3 {
    margin: 0;
    font-size: 14px;
    color: #7cb9f7;
  }

  .ewm dt {
    font-weight: bold;
    line-height: 20px;
  }

  .ewm dd {
    margin-left: 15px;
    line-height: 20px;
  }

  .ewm dd:hover {
    color: #69cb69;
    text-decoration: underline;
  }
</style>

<style lang="scss">
  html.dark .login-wrapper {
    background: #000;
  }
</style>
