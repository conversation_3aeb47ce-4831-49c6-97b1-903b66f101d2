<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <EleTableSearch
      :model="form"
      :items="searchItems"
      :expandAllFilter="searchExpand"
      @updateValue="updateFormValue"
      @submit="search"
      @reset="reset"
    >
      <template #toolbar>
        <el-link
          type="primary"
          underline="never"
          @click="toggleExpand"
          style="margin-left: 12px"
        >
          <template v-if="searchExpand">
            <span>收起</span>
            <el-icon style="vertical-align: -1px">
              <ArrowUp />
            </el-icon>
          </template>
          <template v-else>
            <span>展开</span>
            <el-icon style="vertical-align: -2px">
              <ArrowDown />
            </el-icon>
          </template>
        </el-link>
      </template>
    </EleTableSearch>
  </ele-card>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { ArrowDown, ArrowUp } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserParam } from '@/api/system/user/model';

  const emit = defineEmits<{
    (e: 'search', where?: UserParam): void;
  }>();

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    username: '',
    organizationName: '',
    phone: '',
    email: ''
  });

  /** 日期范围 */
  const dateRange = ref<[string, string]>(['', '']);

  /** 搜索表单是否展开 */
  const searchExpand = ref(false);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '用户账号',
      prop: 'username',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '组织机构',
      prop: 'organizationName',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '手机号',
      prop: 'phone',
      placeholder: '请输入',
      showInExpand: true
    },
    {
      type: 'input',
      label: '邮箱',
      prop: 'email',
      placeholder: '请输入',
      showInExpand: true
    },
    {
      type: 'date-range',
      label: '创建时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD',
      showInExpand: true
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop: string, value: any) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value ?? [];
    emit('search', {
      ...form,
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };

  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
</script>
