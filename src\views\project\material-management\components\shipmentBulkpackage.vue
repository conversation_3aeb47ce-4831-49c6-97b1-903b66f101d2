<template>
  <div style="margin-bottom: 30px">
    <div style="font-weight: bold">散装耗材发运内容</div>
    <ele-pro-table
      row-key="userId"
      :columns="columns"
      :datasource="data"
      :pagination="false"
      :span-method="spanMethod"
      border
    />
  </div>
</template>
<script setup>
  import { ref } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材编号',
      prop: 'bulkConsumableNumber',
      width: 90
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '理论+备用申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '批号',
      prop: 'batchNumber'
    },
    {
      label: '本次发运数量',
      prop: 'quantity'
    },
    {
      label: '有效期至',
      prop: 'endDate'
    }
  ]);
  /** 合并表格单元格 */
  const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < 7) {
      const rowspan = computeSpans('consumableId')[rowIndex];
      return [rowspan, 1];
    }
    return [1, 1];
  };

  // 预处理数据，计算合并的行数
  const computeSpans = (prop) => {
    const spans = [];
    let pos = 0;

    for (let i = 0; i < datasource1.value.length; i++) {
      if (i === 0) {
        spans.push(1);
        pos = 0;
      } else {
        if (datasource1.value[i][prop] === datasource1.value[i - 1][prop]) {
          spans[pos] += 1;
          spans.push(0);
        } else {
          spans.push(1);
          pos = i;
        }
      }
    }
    return spans;
  };
</script>
