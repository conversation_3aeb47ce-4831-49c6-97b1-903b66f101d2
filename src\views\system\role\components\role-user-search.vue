<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    userName: '',
    phonenumber: ''
  });

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '用户名称',
      prop: 'userName',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '手机号码',
      prop: 'phonenumber',
      placeholder: '请输入'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
