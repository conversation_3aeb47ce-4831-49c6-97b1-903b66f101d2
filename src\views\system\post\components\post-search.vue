<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  >
    <template #toolbar>
      <slot name="toolbar"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    postCode: '',
    postName: '',
    status: void 0
  });

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '岗位编码',
      prop: 'postCode',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '岗位名称',
      prop: 'postName',
      placeholder: '请输入'
    },
    {
      type: 'dictSelect',
      label: '状态',
      prop: 'status',
      placeholder: '请选择',
      props: { code: 'sys_normal_disable' }
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
