<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    tableName: '',
    tableComment: ''
  });

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '表名称',
      prop: 'tableName',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '表描述',
      prop: 'tableComment',
      placeholder: '请输入'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
