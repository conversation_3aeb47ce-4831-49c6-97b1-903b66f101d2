<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
    ><template #toolbar>
      <slot name="toolbar"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetForm] = useFormData({
    userName: '',
    phonenumber: ''
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '用户名称',
      prop: 'userName',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '手机号码',
      prop: 'phonenumber',
      placeholder: '请输入'
    },
    {
      type: 'daterange',
      label: '创建时间',
      prop: 'dateRange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (prop === 'dateRange') {
      dateRange.value = value || ['', ''];
      form[prop] = value;
    } else {
      form[prop] = value;
    }
  };

  /** 重置表单数据 */
  const resetFields = () => {
    resetForm();
    dateRange.value = ['', ''];
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', { ...form, params: { beginTime: d1, endTime: d2 } });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
