<!-- 搜索表单 -->
<template>
  <EleTableSearch
    :model="form"
    :show-label="false"
    :items="searchItems"
    @updateValue="updateFormValue"
    @submit="search"
    @reset="reset"
  >
    <template #toolbar>
      <slot name="toolbar"></slot>
    </template>
  </EleTableSearch>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    noticeTitle: '',
    createBy: '',
    noticeType: void 0
  });

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '公告标题',
      prop: 'noticeTitle',
      placeholder: '请输入'
    },
    {
      type: 'input',
      label: '创建者',
      prop: 'createBy',
      placeholder: '请输入'
    },
    {
      type: 'dictSelect',
      label: '公告类型',
      prop: 'noticeType',
      placeholder: '请选择',
      props: { code: 'sys_notice_type' }
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
